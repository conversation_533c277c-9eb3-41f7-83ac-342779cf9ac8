'use client'

import React from 'react'
import { motion } from 'framer-motion'
import BlogPreview from '@/components/BlogPreview'
import EmailPreview from '@/components/EmailPreview'
import SocialMediaPreview from '@/components/SocialMediaPreview'
import VideoScriptPreview from '@/components/VideoScriptPreview'
import VideoAlchemyPreview from '@/components/VideoAlchemyPreview'
import InvinciblePreview from '@/components/InvinciblePreview'
import MegatronPreview from '@/components/MegatronPreview'

const previews = [
  {
    title: 'Email Generator',
    subtitle: 'Professional Email Campaigns',
    component: EmailPreview,
    color: 'from-emerald-500 to-teal-600'
  },
  {
    title: 'Blog Generator',
    subtitle: 'SEO-Optimized Content',
    component: BlogPreview,
    color: 'from-blue-500 to-indigo-600'
  },
  {
    title: 'Social Media',
    subtitle: 'Viral Content Creation',
    component: SocialMediaPreview,
    color: 'from-pink-500 to-rose-600'
  },
  {
    title: 'YouTube Scripts',
    subtitle: 'Video Content Scripts',
    component: VideoScriptPreview,
    color: 'from-red-500 to-orange-600'
  },
  {
    title: 'Video Alchemy',
    subtitle: 'AI Video Generation',
    component: VideoAlchemyPreview,
    color: 'from-purple-500 to-violet-600'
  },
  {
    title: 'Invincible .1V',
    subtitle: 'Super Content Agent',
    component: InvinciblePreview,
    color: 'from-violet-500 to-purple-600'
  },
  {
    title: 'Megatron',
    subtitle: 'Ultimate AI Assistant',
    component: MegatronPreview,
    color: 'from-gray-600 to-slate-700'
  }
]

export default function PreviewShowcase() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-5xl font-bold text-white mb-4">
            Tool Preview Showcase
          </h1>
          <p className="text-xl text-gray-300">
            Beautiful, lightweight interactive previews for all dashboard tools
          </p>
        </motion.div>

        {/* Preview Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {previews.map((preview, index) => {
            const PreviewComponent = preview.component
            return (
              <motion.div
                key={preview.title}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className="group relative"
              >
                {/* Glow Effect */}
                <div className={`absolute inset-0 bg-gradient-to-br ${preview.color} opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-2xl blur-xl`} />
                
                {/* Preview Card */}
                <div className="relative backdrop-blur-xl border border-white/10 rounded-2xl overflow-hidden bg-black/40 hover:border-white/20 transition-all duration-300">
                  {/* Header */}
                  <div className="p-4 border-b border-white/10">
                    <h3 className="text-white font-bold text-lg">{preview.title}</h3>
                    <p className="text-gray-400 text-sm">{preview.subtitle}</p>
                  </div>
                  
                  {/* Preview Area */}
                  <div className="h-[300px] relative">
                    <PreviewComponent />
                  </div>
                  
                  {/* Footer */}
                  <div className="p-4 border-t border-white/10">
                    <div className="flex items-center justify-between">
                      <span className="text-green-400 text-sm font-medium">● Active</span>
                      <span className="text-gray-400 text-xs">Interactive Preview</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="text-center mt-16"
        >
          <p className="text-gray-400">
            All previews are lightweight, performant, and showcase the unique capabilities of each tool.
          </p>
        </motion.div>
      </div>
    </div>
  )
}
