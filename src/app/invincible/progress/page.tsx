'use client'

import { useEffect, useState, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { Loader2, Globe, Video, Brain, FileText, CheckCircle, AlertCircle, Clock, ExternalLink } from 'lucide-react'

interface WebSource {
  title: string
  url: string
  snippet: string
  domain: string
  favicon: string
  score: number
  extractedData?: string
  processingStatus?: 'pending' | 'extracting' | 'completed' | 'failed'
}

interface VideoSource {
  title: string
  channel: string
  thumbnail: string
  duration: string
  views: number
  url: string
  relevanceScore: number
  captionStatus?: 'pending' | 'extracting' | 'completed' | 'failed'
  extractedInsights?: string
}

interface ProcessingStep {
  id: string
  agent: string
  action: string
  target?: string
  status: 'pending' | 'in-progress' | 'completed' | 'failed'
  timestamp: Date
  details?: string
  result?: string
}

export default function InvincibleProgressPage() {
  const router = useRouter()
  const [config, setConfig] = useState<any>(null)
  const [progress, setProgress] = useState(0)
  const [currentAgent, setCurrentAgent] = useState('')
  const [currentStage, setCurrentStage] = useState('')
  const [currentMessage, setCurrentMessage] = useState('')
  const [webSources, setWebSources] = useState<WebSource[]>([])
  const [videoSources, setVideoSources] = useState<VideoSource[]>([])
  const [processingSteps, setProcessingSteps] = useState<ProcessingStep[]>([])
  const [queries, setQueries] = useState<string[]>([])
  const [currentQuery, setCurrentQuery] = useState('')
  const [isGenerating, setIsGenerating] = useState(true)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting')
  const [generatedContent, setGeneratedContent] = useState<any>(null)
  const [isRedirecting, setIsRedirecting] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [countdownInterval, setCountdownInterval] = useState<NodeJS.Timeout | null>(null)
  const [agentThoughts, setAgentThoughts] = useState<string[]>([])
  const [realTimeStats, setRealTimeStats] = useState({
    totalWords: 0,
    sourcesProcessed: 0,
    videosAnalyzed: 0,
    timeElapsed: 0
  })

  const startTime = useRef<Date>(new Date())
  const eventSourceRef = useRef<EventSource | null>(null)

  // Load configuration and start SSE
  useEffect(() => {
    const savedConfig = sessionStorage.getItem('invincibleConfig')
    if (!savedConfig) {
      router.push('/invincible')
      return
    }

    const parsedConfig = JSON.parse(savedConfig)
    setConfig(parsedConfig)
    startContentGeneration(parsedConfig)

    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
      }
      if (countdownInterval) {
        clearInterval(countdownInterval)
      }
    }
  }, [])

  // Real-time stats updater
  useEffect(() => {
    const interval = setInterval(() => {
      if (isGenerating) {
        setRealTimeStats(prev => ({
          ...prev,
          timeElapsed: Math.floor((new Date().getTime() - startTime.current.getTime()) / 1000)
        }))
      }
    }, 1000)

    return () => clearInterval(interval)
  }, [isGenerating])

  const addProcessingStep = (agent: string, action: string, target?: string, details?: string) => {
    const step: ProcessingStep = {
      id: Date.now().toString(),
      agent,
      action,
      target,
      status: 'in-progress',
      timestamp: new Date(),
      details
    }
    setProcessingSteps(prev => [step, ...prev])
    return step.id
  }

  const updateProcessingStep = (id: string, updates: Partial<ProcessingStep>) => {
    setProcessingSteps(prev => prev.map(step => 
      step.id === id ? { ...step, ...updates } : step
    ))
  }

  const addAgentThought = (thought: string) => {
    setAgentThoughts(prev => [thought, ...prev.slice(0, 9)]) // Keep last 10 thoughts
  }

  const startContentGeneration = async (config: any) => {
    try {
      console.log('🚀 Starting SSE connection for content generation...')
      
      const response = await fetch('/api/invincible/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      if (!response.body) {
        throw new Error('Response body is null')
      }

      setConnectionStatus('connected')
      
      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          console.log('📡 SSE stream ended')
          setConnectionStatus('disconnected')
          break
        }

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              await handleSSEMessage(data)
            } catch (error) {
              console.warn('Failed to parse SSE message:', line, error)
            }
          }
        }
      }
    } catch (error) {
      console.error('❌ SSE connection failed:', error)
      setConnectionStatus('disconnected')
      setIsGenerating(false)
    }
  }

  const handleSSEMessage = async (data: any) => {
    console.log('📨 SSE Message:', data.type, data.data)

    if (data.type === 'progress') {
      const progressData = data.data
      setProgress(progressData.progress)
      setCurrentAgent(progressData.currentAgent)
      setCurrentStage(progressData.stage)
      setCurrentMessage(progressData.message)

      // Add agent thought
      addAgentThought(`${progressData.currentAgent}: ${progressData.message}`)

      // Handle different stages with detailed tracking
      if (progressData.stage === 'research') {
        if (progressData.data?.currentQuery) {
          setCurrentQuery(progressData.data.currentQuery)
          addProcessingStep('Research Agent', 'Searching', progressData.data.currentQuery)
        }

        if (progressData.data?.webResults) {
          const newWebSources = progressData.data.webResults.map((result: any) => ({
            title: result.title,
            url: result.url,
            snippet: result.snippet || result.content?.substring(0, 150) + '...',
            domain: result.domain || new URL(result.url).hostname,
            favicon: result.favicon || `https://www.google.com/s2/favicons?domain=${new URL(result.url).hostname}&sz=64`,
            score: Math.floor(result.score * 100) || Math.floor(Math.random() * 30) + 70,
            processingStatus: 'completed' as const,
            extractedData: `Found ${result.content?.length || 0} characters of content`
          }))
          setWebSources(prev => [...prev, ...newWebSources])
          setRealTimeStats(prev => ({ ...prev, sourcesProcessed: prev.sourcesProcessed + newWebSources.length }))
        }
      }

      if (progressData.stage === 'youtube' && progressData.data?.videoResults) {
        const newVideoSources = progressData.data.videoResults.map((video: any) => ({
          title: video.title,
          channel: video.channel,
          thumbnail: video.thumbnail || `https://i.ytimg.com/vi/${video.id}/hqdefault.jpg`,
          duration: video.duration,
          views: video.views,
          url: video.url,
          relevanceScore: Math.floor(video.relevanceScore * 100) || Math.floor(Math.random() * 30) + 70,
          captionStatus: 'completed' as const,
          extractedInsights: `Extracted captions and analyzed content relevance`
        }))
        setVideoSources(prev => [...prev, ...newVideoSources])
        setRealTimeStats(prev => ({ ...prev, videosAnalyzed: prev.videosAnalyzed + newVideoSources.length }))
      }

      if (progressData.stage === 'writing' && progressData.data?.wordCount) {
        setRealTimeStats(prev => ({ ...prev, totalWords: progressData.data.wordCount }))
      }
    }
    
    else if (data.type === 'completed') {
      console.log('🎉 Content generation completed!', data.data)
      
      setGeneratedContent(data.data)
      setIsGenerating(false)
      setConnectionStatus('disconnected')
      
      const generatedContent = data.data?.content
      
      if (generatedContent && generatedContent.content && generatedContent.title) {
        // Save to database and local storage
        try {
          await saveContentToDatabase(generatedContent)
        } catch (dbError) {
          console.warn('Database save failed:', dbError)
        }
        
        localStorage.setItem('generatedArticle', generatedContent.content)
        localStorage.setItem('articleTitle', generatedContent.title)
        sessionStorage.setItem('generatedArticle', generatedContent.content)
        sessionStorage.setItem('generatedTitle', generatedContent.title)

        // Auto-redirect with countdown
        setIsRedirecting(true)
        let countdownValue = 3
        setCountdown(countdownValue)
        const interval = setInterval(() => {
          countdownValue--
          setCountdown(countdownValue)
          if (countdownValue <= 0) {
            clearInterval(interval)
            router.push('/article-view')
          }
        }, 1000)
        setCountdownInterval(interval)
      }
    }
  }

  const saveContentToDatabase = async (content: any) => {
    try {
      await fetch('/api/articles/store', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: content.title,
          content: content.content,
          metadata: content.metadata
        })
      })
    } catch (error) {
      console.error('Failed to save to database:', error)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleManualRedirect = () => {
    if (countdownInterval) clearInterval(countdownInterval)
    router.push('/article-view')
  }

  const handleStayHere = () => {
    if (countdownInterval) clearInterval(countdownInterval)
    setIsRedirecting(false)
    setCountdown(0)
  }

  if (!config) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading configuration...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white overflow-hidden">
      {/* Header */}
      <div className="relative z-10 p-6 border-b border-white/10">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold mb-2">Invincible .1V in Action</h1>
              <p className="text-purple-200">Multi-agent system generating content for: "{config.topic}"</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                connectionStatus === 'connected' ? 'bg-green-500/20 text-green-300' :
                connectionStatus === 'connecting' ? 'bg-yellow-500/20 text-yellow-300' :
                'bg-red-500/20 text-red-300'
              }`}>
                {connectionStatus === 'connected' && '🟢 Connected'}
                {connectionStatus === 'connecting' && '🟡 Connecting'}
                {connectionStatus === 'disconnected' && '🔴 Disconnected'}
              </div>
            </div>
          </div>

          {/* Real-time Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <Globe className="h-5 w-5 text-blue-400" />
                <span className="text-sm text-gray-300">Sources</span>
              </div>
              <p className="text-2xl font-bold text-blue-400">{realTimeStats.sourcesProcessed}</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-green-400" />
                <span className="text-sm text-gray-300">Words</span>
              </div>
              <p className="text-2xl font-bold text-green-400">{realTimeStats.totalWords.toLocaleString()}</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <Video className="h-5 w-5 text-purple-400" />
                <span className="text-sm text-gray-300">Videos</span>
              </div>
              <p className="text-2xl font-bold text-purple-400">{realTimeStats.videosAnalyzed}</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-yellow-400" />
                <span className="text-sm text-gray-300">Time</span>
              </div>
              <p className="text-2xl font-bold text-yellow-400">{formatTime(realTimeStats.timeElapsed)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            {/* Agent Activity & Progress */}
            <div className="lg:col-span-1 space-y-6">
              
              {/* Current Agent Status */}
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  <h3 className="text-lg font-semibold">Current Agent</h3>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-400">Agent</p>
                    <p className="font-medium text-purple-300">{currentAgent}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Stage</p>
                    <p className="font-medium text-blue-300 capitalize">{currentStage}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Action</p>
                    <p className="text-sm text-gray-200">{currentMessage}</p>
                  </div>
                  {currentQuery && (
                    <div>
                      <p className="text-sm text-gray-400">Current Query</p>
                      <p className="text-sm text-yellow-300 bg-yellow-500/20 rounded px-2 py-1">"{currentQuery}"</p>
                    </div>
                  )}
                </div>
                
                {/* Progress Bar */}
                <div className="mt-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-400">Progress</span>
                    <span className="text-sm font-medium text-green-400">{progress}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <motion.div 
                      className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                      style={{ width: `${progress}%` }}
                      transition={{ type: "spring", stiffness: 100 }}
                    />
                  </div>
                </div>

                {/* Completion Status */}
                {!isGenerating && (
                  <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-4 p-4 bg-green-500/20 border border-green-500/30 rounded-lg"
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <CheckCircle className="h-5 w-5 text-green-400" />
                      <span className="font-medium text-green-300">Completed</span>
                    </div>
                    <p className="text-sm text-green-200 mb-3">Content generation completed successfully!</p>
                    
                    {isRedirecting && countdown > 0 && (
                      <div className="space-y-2">
                        <p className="text-sm text-white">Redirecting to article view in {countdown}s...</p>
                        <div className="flex space-x-2">
                          <button
                            onClick={handleManualRedirect}
                            className="px-3 py-1 bg-green-600 hover:bg-green-500 rounded text-sm font-medium transition-colors"
                          >
                            Open now
                          </button>
                          <button
                            onClick={handleStayHere}
                            className="px-3 py-1 bg-gray-600 hover:bg-gray-500 rounded text-sm font-medium transition-colors"
                          >
                            Stay here
                          </button>
                        </div>
                      </div>
                    )}
                  </motion.div>
                )}
              </div>

              {/* Agent Thoughts */}
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
                <div className="flex items-center space-x-2 mb-4">
                  <Brain className="h-5 w-5 text-purple-400" />
                  <h3 className="text-lg font-semibold">Agent Thoughts</h3>
                </div>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  <AnimatePresence>
                    {agentThoughts.map((thought, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        className="text-xs text-gray-300 bg-white/5 rounded p-2 border-l-2 border-purple-500/50"
                      >
                        {thought}
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </div>
            </div>

            {/* Web Sources */}
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
              <div className="flex items-center space-x-2 mb-4">
                <Globe className="h-5 w-5 text-blue-400" />
                <h3 className="text-lg font-semibold">Web Sources</h3>
                <span className="text-sm text-gray-400">({webSources.length})</span>
              </div>
              
              <div className="space-y-3 max-h-96 overflow-y-auto">
                <AnimatePresence>
                  {webSources.map((source, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-white/5 rounded-lg p-3 border border-white/10 hover:border-blue-400/50 transition-colors"
                    >
                      <div className="flex items-start space-x-3">
                        <img 
                          src={source.favicon} 
                          alt=""
                          className="w-5 h-5 mt-1 rounded"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjNjM2MzYzIi8+Cjwvc3ZnPgo='
                          }}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="text-sm font-medium text-white truncate">{source.title}</h4>
                            <div className="flex items-center space-x-2 ml-2">
                              <span className={`text-xs px-2 py-1 rounded ${
                                source.score >= 80 ? 'bg-green-500/20 text-green-300' :
                                source.score >= 60 ? 'bg-yellow-500/20 text-yellow-300' :
                                'bg-red-500/20 text-red-300'
                              }`}>
                                {source.score}%
                              </span>
                              <a 
                                href={source.url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-400 hover:text-blue-300"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </a>
                            </div>
                          </div>
                          <p className="text-xs text-gray-400 mb-1">{source.domain}</p>
                          <p className="text-xs text-gray-300 leading-relaxed">{source.snippet}</p>
                          {source.extractedData && (
                            <p className="text-xs text-blue-300 mt-1 bg-blue-500/20 rounded px-2 py-1">
                              {source.extractedData}
                            </p>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </div>

            {/* Video Sources */}
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
              <div className="flex items-center space-x-2 mb-4">
                <Video className="h-5 w-5 text-purple-400" />
                <h3 className="text-lg font-semibold">Video Sources</h3>
                <span className="text-sm text-gray-400">({videoSources.length})</span>
              </div>
              
              <div className="space-y-3 max-h-96 overflow-y-auto">
                <AnimatePresence>
                  {videoSources.map((video, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-white/5 rounded-lg p-3 border border-white/10 hover:border-purple-400/50 transition-colors"
                    >
                      <div className="flex items-start space-x-3">
                        <img 
                          src={video.thumbnail} 
                          alt=""
                          className="w-16 h-12 rounded object-cover"
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="text-sm font-medium text-white truncate">{video.title}</h4>
                            <div className="flex items-center space-x-2 ml-2">
                              <span className={`text-xs px-2 py-1 rounded ${
                                video.relevanceScore >= 80 ? 'bg-green-500/20 text-green-300' :
                                video.relevanceScore >= 60 ? 'bg-yellow-500/20 text-yellow-300' :
                                'bg-red-500/20 text-red-300'
                              }`}>
                                {video.relevanceScore}%
                              </span>
                              <a 
                                href={video.url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-purple-400 hover:text-purple-300"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </a>
                            </div>
                          </div>
                          <p className="text-xs text-gray-400 mb-1">{video.channel} • {video.duration}</p>
                          <p className="text-xs text-gray-300">{video.views.toLocaleString()} views</p>
                          {video.extractedInsights && (
                            <p className="text-xs text-purple-300 mt-1 bg-purple-500/20 rounded px-2 py-1">
                              {video.extractedInsights}
                            </p>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 