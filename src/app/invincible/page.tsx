'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  Zap, 
  Sparkles, 
  Brain, 
  Search, 
  Play, 
  Target, 
  Globe,
  Wand2,
  Rocket,
  Star,
  CheckCircle,
  ArrowRight,
  Settings,
  Users,
  FileText,
  Youtube,
  Lightbulb,
  Crown,
  Clock
} from 'lucide-react';
import Link from 'next/link';

export default function InvincibleInputPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [topic, setTopic] = useState('')
  const [wordCount, setWordCount] = useState(1500)
  const [tone, setTone] = useState('Professional and informative')
  const [targetAudience, setTargetAudience] = useState('')
  const [includeYouTube, setIncludeYouTube] = useState(true)
  const [additionalInstructions, setAdditionalInstructions] = useState('')
  const [isStarting, setIsStarting] = useState(false)

  // Redirect if not authenticated
  if (status === 'unauthenticated') {
    router.push('/login')
    return null
  }

  const handleStartGeneration = async () => {
    if (!topic.trim()) return
    
    setIsStarting(true)
    
    // Store the configuration in sessionStorage for the progress page
    const config = {
      topic,
      wordCount,
      tone,
      targetAudience,
      includeYouTube,
      additionalInstructions,
      timestamp: Date.now()
    }
    
    sessionStorage.setItem('invincibleConfig', JSON.stringify(config))
    
    // Navigate to progress page
    router.push('/invincible/progress')
  }

  const features = [
    {
      icon: Brain,
      title: "Multi-Agent Intelligence",
      description: "Coordinated AI agents work together for superior content",
      color: "from-violet-500 to-purple-500"
    },
    {
      icon: Search,
      title: "Deep Web Research",
      description: "Comprehensive web scraping and competitive analysis",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: Play,
      title: "YouTube Integration",
      description: "Extract insights from relevant video content",
      color: "from-red-500 to-pink-500"
    },
    {
      icon: Wand2,
      title: "SEO Optimization",
      description: "Built-in SEO, AEO, and GEO optimization",
      color: "from-emerald-500 to-green-500"
    }
  ]

  const toneOptions = [
    { value: "Professional and informative", label: "Professional", icon: Target },
    { value: "Casual and conversational", label: "Casual", icon: Users },
    { value: "Academic and formal", label: "Academic", icon: Brain },
    { value: "Creative and engaging", label: "Creative", icon: Sparkles }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-900/20 via-black to-indigo-900/20" />
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-500/10 rounded-full blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-500/10 rounded-full blur-[120px]"
        />
      </div>

      {/* Navigation */}
      <nav className="relative z-10 p-6">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <Link href="/dashboard" className="flex items-center space-x-3 text-gray-300 hover:text-white transition-colors">
            <div className="p-2 bg-white/10 rounded-lg backdrop-blur-sm">
              <Crown className="w-5 h-5" />
            </div>
            <span className="font-medium">Dashboard</span>
          </Link>
          
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-400">
              {session?.user?.email}
            </div>
          </div>
        </div>
      </nav>

      <div className="relative z-10 max-w-7xl mx-auto px-6 py-8">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center mb-8">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-purple-600 rounded-3xl blur-lg opacity-70" />
              <div className="relative p-4 bg-gradient-to-r from-violet-600 to-purple-600 rounded-3xl">
                <Zap className="w-12 h-12 text-white" />
              </div>
            </div>
          </div>
          
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            Invincible <span className="text-transparent bg-clip-text bg-gradient-to-r from-violet-400 to-purple-400">.1V</span>
          </h1>
          
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            The most advanced AI content creation system. Multi-agent intelligence, 
            comprehensive research, and superior writing capabilities combined.
          </p>
          
          <div className="flex flex-wrap justify-center gap-3">
            <div className="flex items-center space-x-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20">
              <Star className="w-4 h-4 text-yellow-400" />
              <span className="text-sm text-gray-300">Multi-Agent AI</span>
            </div>
            <div className="flex items-center space-x-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20">
              <CheckCircle className="w-4 h-4 text-emerald-400" />
              <span className="text-sm text-gray-300">SEO Optimized</span>
            </div>
            <div className="flex items-center space-x-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20">
              <Rocket className="w-4 h-4 text-blue-400" />
              <span className="text-sm text-gray-300">Superior Quality</span>
            </div>
          </div>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
              className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
            >
              <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4`}>
                <feature.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
              <p className="text-gray-300 text-sm">{feature.description}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* Main Content Form */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Configuration Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="lg:col-span-2 bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-8"
          >
            <div className="flex items-center space-x-3 mb-8">
              <div className="p-2 bg-gradient-to-r from-violet-600 to-purple-600 rounded-lg">
                <Settings className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-white">Content Configuration</h2>
            </div>
            
            <div className="space-y-6">
              {/* Topic Input */}
              <div>
                <label className="flex items-center space-x-2 text-gray-300 mb-3">
                  <Lightbulb className="w-4 h-4" />
                  <span className="font-medium">Topic *</span>
                </label>
                <input
                  type="text"
                  value={topic}
                  onChange={(e) => setTopic(e.target.value)}
                  placeholder="Enter your content topic (e.g., 'Best AI tools for productivity')"
                  className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all"
                />
              </div>

              {/* Word Count and Tone */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="flex items-center space-x-2 text-gray-300 mb-3">
                    <FileText className="w-4 h-4" />
                    <span className="font-medium">Word Count</span>
                  </label>
                  <input
                    type="number"
                    value={wordCount}
                    onChange={(e) => setWordCount(parseInt(e.target.value) || 1500)}
                    min="500"
                    max="5000"
                    className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all"
                  />
                </div>
                
                <div>
                  <label className="flex items-center space-x-2 text-gray-300 mb-3">
                    <Target className="w-4 h-4" />
                    <span className="font-medium">Writing Tone</span>
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    {toneOptions.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => setTone(option.value)}
                        className={`flex items-center space-x-2 p-3 rounded-lg transition-all ${
                          tone === option.value
                            ? 'bg-violet-600/30 border-violet-500 text-white'
                            : 'bg-white/5 border-white/20 text-gray-300 hover:bg-white/10'
                        } border`}
                      >
                        <option.icon className="w-4 h-4" />
                        <span className="text-sm font-medium">{option.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Target Audience */}
              <div>
                <label className="flex items-center space-x-2 text-gray-300 mb-3">
                  <Users className="w-4 h-4" />
                  <span className="font-medium">Target Audience</span>
                </label>
                <input
                  type="text"
                  value={targetAudience}
                  onChange={(e) => setTargetAudience(e.target.value)}
                  placeholder="e.g., Business professionals, Students, General audience"
                  className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all"
                />
              </div>

              {/* YouTube Integration */}
              <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-red-600/20 rounded-lg">
                      <Youtube className="w-5 h-5 text-red-400" />
                    </div>
                    <div>
                      <h4 className="text-white font-medium">YouTube Research</h4>
                      <p className="text-sm text-gray-400">Extract insights from relevant videos</p>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={includeYouTube}
                      onChange={(e) => setIncludeYouTube(e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="relative w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-violet-600"></div>
                  </label>
                </div>
              </div>

              {/* Additional Instructions */}
              <div>
                <label className="flex items-center space-x-2 text-gray-300 mb-3">
                  <Sparkles className="w-4 h-4" />
                  <span className="font-medium">Additional Instructions</span>
                </label>
                <textarea
                  value={additionalInstructions}
                  onChange={(e) => setAdditionalInstructions(e.target.value)}
                  placeholder="Any specific requirements, focus areas, or style preferences..."
                  rows={4}
                  className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent transition-all resize-none"
                />
              </div>
            </div>
          </motion.div>

          {/* Action Panel */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-6"
          >
            {/* Generation Button */}
            <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Ready to Generate?</h3>
              <p className="text-gray-300 text-sm mb-6">
                Our AI agents will research, analyze, and create superior content for you.
              </p>
              
              <motion.button
                onClick={handleStartGeneration}
                disabled={!topic.trim() || isStarting}
                whileHover={{ scale: topic.trim() ? 1.02 : 1 }}
                whileTap={{ scale: topic.trim() ? 0.98 : 1 }}
                className="w-full py-4 bg-gradient-to-r from-violet-600 to-purple-600 text-white rounded-xl font-semibold hover:shadow-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 flex items-center justify-center space-x-2"
              >
                {isStarting ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    <span>Starting...</span>
                  </>
                ) : (
                  <>
                    <Rocket className="w-5 h-5" />
                    <span>Generate Supreme Content</span>
                    <ArrowRight className="w-5 h-5" />
                  </>
                )}
              </motion.button>
            </div>

            {/* Preview Panel */}
            <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Configuration Preview</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Topic:</span>
                  <span className="text-white truncate ml-2">{topic || 'Not set'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Words:</span>
                  <span className="text-white">{wordCount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Tone:</span>
                  <span className="text-white">{tone.split(' ')[0]}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">YouTube:</span>
                  <span className={includeYouTube ? 'text-emerald-400' : 'text-gray-400'}>
                    {includeYouTube ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
              </div>
            </div>

            {/* Stats Panel */}
            <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Expected Output</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-blue-400" />
                    <span className="text-gray-300 text-sm">Processing Time</span>
                  </div>
                  <span className="text-white text-sm">~3-5 min</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Globe className="w-4 h-4 text-emerald-400" />
                    <span className="text-gray-300 text-sm">Web Sources</span>
                  </div>
                  <span className="text-white text-sm">5-15</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Play className="w-4 h-4 text-red-400" />
                    <span className="text-gray-300 text-sm">Video Sources</span>
                  </div>
                  <span className="text-white text-sm">{includeYouTube ? '3-8' : '0'}</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
