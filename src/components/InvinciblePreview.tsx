'use client'

import { useRef, useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Zap, Search, Youtube, Brain, Globe, TrendingUp, Target, Sparkles } from 'lucide-react'

// Invincible .1V Super Agent Preview
function InvincibleAgent() {
  const [currentPhase, setCurrentPhase] = useState(0)
  const [agentProgress, setAgentProgress] = useState(0)
  const [activeAgents, setActiveAgents] = useState<string[]>([])
  const [researchData, setResearchData] = useState({
    webPages: 0,
    youtubeVideos: 0,
    insights: 0,
    quality: 0
  })

  const phases = [
    {
      name: 'Research Agent',
      icon: Search,
      color: '#3B82F6',
      description: 'Deep web research & analysis',
      action: 'Analyzing 10 web sources...'
    },
    {
      name: 'YouTube Agent',
      icon: Youtube,
      color: '#EF4444',
      description: 'Video content analysis',
      action: 'Processing video transcripts...'
    },
    {
      name: 'Intelligence Agent',
      icon: Brain,
      color: '#8B5CF6',
      description: 'Competitive intelligence',
      action: 'Identifying content gaps...'
    },
    {
      name: 'Content Agent',
      icon: Sparkles,
      color: '#F59E0B',
      description: 'Superior content creation',
      action: 'Generating optimized content...'
    }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentPhase(prev => (prev + 1) % phases.length)
      setAgentProgress(0)
      
      // Simulate agent activation
      setActiveAgents(prev => {
        const newAgents = [...prev]
        const currentAgent = phases[currentPhase].name
        if (!newAgents.includes(currentAgent)) {
          newAgents.push(currentAgent)
        }
        return newAgents.slice(-3) // Keep last 3 active
      })
    }, 3000)

    return () => clearInterval(interval)
  }, [currentPhase])

  useEffect(() => {
    const progressInterval = setInterval(() => {
      setAgentProgress(prev => {
        const newProgress = prev + 2
        
        // Update research data based on progress
        if (newProgress % 20 === 0) {
          setResearchData(prev => ({
            webPages: Math.min(10, prev.webPages + 1),
            youtubeVideos: Math.min(5, prev.youtubeVideos + (Math.random() > 0.7 ? 1 : 0)),
            insights: Math.min(25, prev.insights + Math.floor(Math.random() * 3) + 1),
            quality: Math.min(98, prev.quality + 0.5)
          }))
        }
        
        return newProgress >= 100 ? 0 : newProgress
      })
    }, 100)

    return () => clearInterval(progressInterval)
  }, [])

  return (
    <div className="relative w-full h-full bg-gradient-to-br from-violet-950/40 via-purple-950/40 to-indigo-950/40 p-2 sm:p-4 overflow-hidden">
      {/* Animated Background Particles */}
      <div className="absolute inset-0">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-violet-400/30 rounded-full"
            style={{
              left: `${15 + (i * 15)}%`,
              top: `${25 + (i % 2) * 30}%`,
            }}
            animate={{
              y: [0, -15, 0],
              opacity: [0.3, 1, 0.3],
              scale: [0.5, 1.2, 0.5],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 0.5,
            }}
          />
        ))}
      </div>

      {/* Main Agent Hub */}
      <div className="relative z-10 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between mb-3 sm:mb-4">
          <div className="flex items-center space-x-2">
            <motion.div
              animate={{
                rotate: [0, 360],
                scale: [1, 1.1, 1]
              }}
              transition={{ duration: 4, repeat: Infinity }}
              className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-violet-500 to-purple-600 rounded-lg flex items-center justify-center"
            >
              <Zap className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
            </motion.div>
            <div>
              <h3 className="text-white font-bold text-xs sm:text-sm">Invincible .1V</h3>
              <p className="text-violet-300 text-[10px] sm:text-xs">Super Agent Active</p>
            </div>
          </div>

          <motion.div
            animate={{ scale: [1, 1.05, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="bg-green-500/20 text-green-400 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-[10px] sm:text-xs font-semibold border border-green-500/30"
          >
            ● LIVE
          </motion.div>
        </div>

        {/* Active Phase Display */}
        <div className="bg-black/30 backdrop-blur-xl rounded-lg border border-white/10 p-3 mb-4">
          <div className="flex items-center space-x-3">
            <motion.div
              key={currentPhase}
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              className="w-10 h-10 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: phases[currentPhase].color + '20' }}
            >
              <phases[currentPhase].icon 
                className="w-5 h-5" 
                style={{ color: phases[currentPhase].color }}
              />
            </motion.div>
            
            <div className="flex-1">
              <h4 className="text-white font-semibold text-sm">{phases[currentPhase].name}</h4>
              <p className="text-gray-400 text-xs">{phases[currentPhase].action}</p>
              
              {/* Progress Bar */}
              <div className="w-full bg-white/10 rounded-full h-1.5 mt-2">
                <motion.div
                  className="h-full rounded-full"
                  style={{ backgroundColor: phases[currentPhase].color }}
                  animate={{ width: `${agentProgress}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Research Metrics */}
        <div className="grid grid-cols-2 gap-2 mb-4">
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-2">
            <div className="flex items-center space-x-2">
              <Globe className="w-3 h-3 text-blue-400" />
              <span className="text-blue-300 text-xs font-medium">Web Sources</span>
            </div>
            <p className="text-white font-bold text-lg">{researchData.webPages}/10</p>
          </div>
          
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-2">
            <div className="flex items-center space-x-2">
              <Youtube className="w-3 h-3 text-red-400" />
              <span className="text-red-300 text-xs font-medium">Videos</span>
            </div>
            <p className="text-white font-bold text-lg">{researchData.youtubeVideos}/5</p>
          </div>
          
          <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-2">
            <div className="flex items-center space-x-2">
              <Brain className="w-3 h-3 text-purple-400" />
              <span className="text-purple-300 text-xs font-medium">Insights</span>
            </div>
            <p className="text-white font-bold text-lg">{researchData.insights}</p>
          </div>
          
          <div className="bg-amber-500/10 border border-amber-500/20 rounded-lg p-2">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-3 h-3 text-amber-400" />
              <span className="text-amber-300 text-xs font-medium">Quality</span>
            </div>
            <p className="text-white font-bold text-lg">{researchData.quality.toFixed(1)}%</p>
          </div>
        </div>

        {/* Active Agents */}
        <div className="flex-1">
          <h5 className="text-white/80 text-xs font-medium mb-2">Active Agents</h5>
          <div className="space-y-1">
            <AnimatePresence>
              {activeAgents.map((agent, index) => (
                <motion.div
                  key={agent}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="flex items-center space-x-2 bg-white/5 rounded-lg p-2"
                >
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1.5, repeat: Infinity, delay: index * 0.3 }}
                    className="w-2 h-2 bg-green-400 rounded-full"
                  />
                  <span className="text-white/80 text-xs">{agent}</span>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>

        {/* Bottom Status */}
        <motion.div
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="absolute -bottom-1 -right-2 bg-violet-600/20 backdrop-blur-2xl text-violet-400 px-3 py-1 rounded-full text-xs font-semibold border border-violet-600/20"
        >
          <motion.span
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
            className="inline-block mr-1"
          >
            ⚡
          </motion.span>
          SUPER AGENT
        </motion.div>
      </div>
    </div>
  )
}

export default function InvinciblePreview() {
  return (
    <div className="w-full h-full bg-gradient-to-br from-violet-950 via-purple-950 to-indigo-950">
      <InvincibleAgent />
    </div>
  )
}
