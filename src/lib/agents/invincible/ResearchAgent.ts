import { enhancedTavilySearch } from '@/lib/tools/tavily-search'
import { OpenRouterService } from '@/lib/openrouter'
import { WebSearchResult, AgentConfig } from './types'

export class ResearchAgent {
  private openRouter: OpenRouterService
  private agentName = 'Research Agent'
  private config: AgentConfig

  constructor(config?: Partial<AgentConfig>) {
    this.openRouter = new OpenRouterService()
    this.config = {
      temperature: 0.7,
      maxTokens: 4000,
      model: 'moonshotai/kimi-k2',
      retryAttempts: 3,
      ...config
    }
  }

  async generateIntelligentQueries(topic: string, initialFindings: string): Promise<string[]> {
    console.log(`🧠 ${this.agentName}: Generating intelligent queries based on initial findings`)

    const prompt = `Based on this initial research about '${topic}', generate 5-6 highly targeted search queries that will uncover:
- Latest trends and developments (2024-2025)
- Expert insights and authoritative perspectives
- Statistical data and research findings
- Practical applications and real-world examples
- Emerging subtopics and related angles
- Industry best practices and case studies

Initial findings summary:
${initialFindings}

IMPORTANT: Return ONLY the search queries, one per line. Do not include numbering, bullets, or explanations. Each query should be a complete search phrase that will find information NOT already covered in the initial search.

Example format:
${topic} advanced features 2025
${topic} implementation best practices
${topic} real world case studies

Generate 5-6 queries now:`

    try {
      const generationResponse = await this.openRouter.generateContent(
        prompt,
        undefined, // no custom system prompt
        {
          temperature: this.config.temperature,
          maxTokens: 1000
        },
        'Intelligent Query Generation'
      )

      const response = generationResponse.response
      console.log(`🔍 ${this.agentName}: Raw AI response: "${response}"`)

      const queries = response.split('\n')
        .map(q => q.trim())
        .map(q => q.replace(/^\d+\.?\s*/, '')) // Remove numbering but keep the query
        .filter(q => q.length > 10) // Only keep substantial queries
        .slice(0, 6) // Limit to 6 queries

      console.log(`🔍 ${this.agentName}: Parsed queries:`, queries)

      if (queries.length === 0) {
        console.warn(`⚠️ ${this.agentName}: No valid queries parsed from AI response, using fallback`)
        return [
          `${topic} 2025 guide`,
          `${topic} best practices`,
          `${topic} tutorial how to`,
          `${topic} examples case studies`,
          `${topic} latest trends`
        ]
      }

      console.log(`✅ ${this.agentName}: Generated ${queries.length} intelligent queries`)
      return queries
    } catch (error) {
      console.warn(`⚠️ ${this.agentName}: Failed to generate intelligent queries, using fallback`)
      // Fallback to basic queries if AI generation fails
      return [
        `${topic} 2025 guide`,
        `${topic} best practices`,
        `${topic} tutorial how to`,
        `${topic} examples case studies`,
        `${topic} latest trends`
      ]
    }
  }

  async generateSearchQueries(topic: string, targetAudience?: string): Promise<string[]> {
    console.log(`🔍 ${this.agentName}: Starting intelligent multi-phase research for "${topic}"`)

    // PHASE 1: Exact topic search
    console.log(`📋 ${this.agentName}: PHASE 1 - Conducting exact topic search`)
    const exactTopicResults = await this.conductSingleSearch(topic)

    // Summarize initial findings for intelligent query generation
    const initialFindings = this.summarizeSearchResults(exactTopicResults)

    // PHASE 2: Generate intelligent queries based on initial findings
    console.log(`🧠 ${this.agentName}: PHASE 2 - Generating intelligent queries based on findings`)
    const intelligentQueries = await this.generateIntelligentQueries(topic, initialFindings)

    // Combine exact topic with intelligent queries
    const allQueries = [topic, ...intelligentQueries]

    console.log(`✅ ${this.agentName}: Generated ${allQueries.length} total queries (1 exact + ${intelligentQueries.length} intelligent)`)
    return allQueries
  }

  private async conductSingleSearch(query: string): Promise<WebSearchResult[]> {
    try {
      console.log(`🔍 Searching: "${query}"`)

      const searchResults = await enhancedTavilySearch.search(query, {
        maxResults: 3,
        searchDepth: 'advanced',
        includeAnswer: true,
        includeRawContent: true,
        temporalFocus: 'current'
      })

      console.log(`🔍 Raw search results structure for "${query}":`, {
        hasResults: !!searchResults,
        type: typeof searchResults,
        keys: searchResults ? Object.keys(searchResults) : [],
        resultCount: (searchResults as any)?.results?.length || 0,
        totalResults: (searchResults as any)?.totalResults || 0
      })

      if (!searchResults || !(searchResults as any).results) {
        console.log(`❌ No results found or invalid structure for "${query}"`)
        return []
      }

      return (searchResults as any).results.map((result: any) => ({
        title: result.title || '',
        url: result.url || '',
        content: result.content || result.raw_content || '',
        snippet: result.content?.substring(0, 300) || '',
        score: result.score || 0.5,
        timestamp: new Date().toISOString()
      }))
    } catch (error) {
      console.warn(`⚠️ Search failed for "${query}":`, error)
      return []
    }
  }

  private summarizeSearchResults(results: WebSearchResult[]): string {
    if (!results || results.length === 0) {
      return "No initial results found."
    }

    const summary = results.slice(0, 3).map(result =>
      `- ${result.title}: ${result.content.substring(0, 200)}...`
    ).join('\n')

    return `Key findings from initial search:\n${summary}`
  }

  async conductWebSearch(queries: string[]): Promise<WebSearchResult[]> {
    console.log(`🌐 ${this.agentName}: PHASE 3 - Conducting targeted research execution with ${queries.length} queries`)

    const allResults: WebSearchResult[] = []

    for (const query of queries) {
      const results = await this.conductSingleSearch(query)
      allResults.push(...results)
      console.log(`✅ Found ${results.length} results for "${query}"`)

      // Small delay to respect rate limits
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    // Remove duplicates based on URL
    const uniqueResults = allResults.filter((result, index, self) => 
      index === self.findIndex(r => r.url === result.url)
    )

    // Sort by score and limit total results
    const sortedResults = uniqueResults
      .sort((a, b) => b.score - a.score)
      .slice(0, 20) // Limit to top 20 results

    console.log(`✅ ${this.agentName}: Collected ${sortedResults.length} unique web results`)
    return sortedResults
  }

  async performIntelligentResearch(topic: string): Promise<{
    phase1Summary: string,
    intelligentQueries: string[],
    allResults: WebSearchResult[],
    keyInsights: string[],
    sourceRepository: { url: string, relevance: number, title: string }[]
  }> {
    console.log(`🔬 ${this.agentName}: Executing intelligent research workflow for "${topic}"`)

    // PHASE 1: Exact topic analysis
    console.log(`📋 ${this.agentName}: PHASE 1 - Conducting exact topic search`)
    const exactTopicResults = await this.conductSingleSearch(topic)
    const phase1Summary = this.summarizeSearchResults(exactTopicResults)

    console.log(`📋 ${this.agentName}: Phase 1 Summary:`)
    console.log(phase1Summary)

    // PHASE 2: Generate intelligent queries
    console.log(`🧠 ${this.agentName}: PHASE 2 - Generating intelligent queries based on findings`)
    const intelligentQueries = await this.generateIntelligentQueries(topic, phase1Summary)

    console.log(`🧠 ${this.agentName}: Generated intelligent queries:`)
    intelligentQueries.forEach((query, index) => {
      console.log(`   ${index + 1}. ${query}`)
    })

    // PHASE 3: Execute all queries (exact + intelligent)
    console.log(`🌐 ${this.agentName}: PHASE 3 - Executing all research queries`)
    const allQueries = [topic, ...intelligentQueries]
    const allResults = await this.conductWebSearch(allQueries)

    // Generate key insights
    const keyInsights = this.extractKeyInsights(allResults, topic)

    // Create source repository
    const sourceRepository = allResults.map(result => ({
      url: result.url,
      relevance: result.score,
      title: result.title
    })).sort((a, b) => b.relevance - a.relevance)

    console.log(`✅ ${this.agentName}: Intelligent research completed`)
    console.log(`📊 Results: ${allResults.length} sources, ${intelligentQueries.length} intelligent queries`)

    return {
      phase1Summary,
      intelligentQueries,
      allResults,
      keyInsights,
      sourceRepository
    }
  }

  private extractKeyInsights(results: WebSearchResult[], topic: string): string[] {
    // Extract key insights from search results
    const insights: string[] = []

    // Look for statistical data, trends, and key facts
    results.slice(0, 10).forEach(result => {
      const content = result.content.toLowerCase()

      // Extract sentences with numbers/statistics
      const sentences = result.content.split(/[.!?]+/)
      sentences.forEach(sentence => {
        if (sentence.match(/\d+%|\d+\s*(million|billion|thousand|users|people|companies)/i) &&
            sentence.length < 200 && sentence.length > 20) {
          insights.push(sentence.trim())
        }
      })
    })

    // Remove duplicates and limit to top insights
    return [...new Set(insights)].slice(0, 8)
  }

  async enhanceSearchResults(results: WebSearchResult[], topic: string): Promise<WebSearchResult[]> {
    console.log(`🧠 ${this.agentName}: Using simple relevance scoring`)

    // Simple relevance scoring based on title and content matching
    const enhancedResults = results.map((result) => {
      const topicWords = topic.toLowerCase().split(' ')
      const titleLower = result.title.toLowerCase()
      const contentLower = result.content.toLowerCase()

      let score = 0.5 // Base score

      // Boost score for topic words in title
      topicWords.forEach(word => {
        if (titleLower.includes(word)) score += 0.2
        if (contentLower.includes(word)) score += 0.1
      })

      // Boost for recent content
      if (result.content.includes('2025') || result.content.includes('2024')) {
        score += 0.1
      }

      return {
        ...result,
        score: Math.min(1.0, score)
      }
    })

    // Sort by enhanced scores
    const sortedResults = enhancedResults.sort((a, b) => b.score - a.score)

    console.log(`✅ ${this.agentName}: Simple relevance scoring completed`)
    return sortedResults
  }

  async performResearch(topic: string, targetAudience?: string): Promise<{
    results: WebSearchResult[]
    queries: string[]
  }> {
    console.log(`🚀 ${this.agentName}: Starting intelligent multi-phase research for "${topic}"`)

    try {
      // Use the new intelligent research approach
      const intelligentResearch = await this.performIntelligentResearch(topic)

      // Enhance results with relevance scoring
      const enhancedResults = await this.enhanceSearchResults(intelligentResearch.allResults, topic)

      console.log(`✅ ${this.agentName}: Intelligent research completed`)
      console.log(`📊 Phase 1 Summary: ${intelligentResearch.phase1Summary.substring(0, 100)}...`)
      console.log(`🧠 Generated ${intelligentResearch.intelligentQueries.length} intelligent queries`)
      console.log(`📚 Found ${enhancedResults.length} total sources`)
      console.log(`💡 Extracted ${intelligentResearch.keyInsights.length} key insights`)

      return {
        results: enhancedResults,
        queries: [topic, ...intelligentResearch.intelligentQueries] // Include exact topic + intelligent queries
      }
    } catch (error) {
      console.error(`❌ ${this.agentName}: Intelligent research failed, falling back to basic research:`, error)

      // Fallback to basic research if intelligent research fails
      try {
        const queries = await this.generateSearchQueries(topic, targetAudience)
        const rawResults = await this.conductWebSearch(queries)
        const enhancedResults = await this.enhanceSearchResults(rawResults, topic)

        console.log(`✅ ${this.agentName}: Fallback research completed - ${enhancedResults.length} sources found`)

        return {
          results: enhancedResults,
          queries
        }
      } catch (fallbackError) {
        console.error(`❌ ${this.agentName}: Both intelligent and fallback research failed:`, fallbackError)
        throw new Error(`Research failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`)
      }
    }
  }
}
