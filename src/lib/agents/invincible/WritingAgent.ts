import { OpenRouterService } from '@/lib/openrouter'
import { GeminiService } from '@/lib/gemini'
import { 
  WebSearchResult, 
  YouTubeVideoData, 
  CompetitiveAnalysis,
  GeneratedContent,
  ContentMetadata,
  SourceReference,
  YouTubeReference,
  InvincibleRequest,
  AgentConfig 
} from './types'

export class WritingAgent {
  private openRouter: OpenRouterService
  private gemini: GeminiService
  private agentName = 'Writing Agent'
  private config: AgentConfig

  constructor(config?: Partial<AgentConfig>) {
    this.openRouter = new OpenRouterService()
    this.gemini = new GeminiService()
    this.config = {
      temperature: 0.7,
      maxTokens: 8000,
      model: 'moonshotai/kimi-k2', // Can switch to Gemini 2.5 Flash Lite
      retryAttempts: 3,
      ...config
    }
  }

  async generateContent(
    request: InvincibleRequest,
    webResults: WebSearchResult[],
    videos: YouTubeVideoData[],
    analysis: CompetitiveAnalysis
  ): Promise<GeneratedContent> {
    console.log(`✍️ ${this.agentName}: Starting content generation for "${request.topic}"`)
    
    // Prepare context data
    const context = this.prepareWritingContext(request, webResults, videos, analysis)
    
    // Generate the main content
    const content = await this.generateMainContent(context)
    
    // Generate title
    const title = await this.generateTitle(request.topic, content)
    
    // Create metadata
    const metadata = this.generateMetadata(content, request.topic)
    
    // Create source references
    const sources = this.createSourceReferences(webResults, content)
    const youtubeReferences = this.createYouTubeReferences(videos, content)
    
    const result: GeneratedContent = {
      title,
      content,
      metadata,
      sources,
      youtubeReferences
    }

    console.log(`✅ ${this.agentName}: Content generation completed`)
    console.log(`📝 Generated ${metadata.wordCount} words with ${sources.length} web sources and ${youtubeReferences.length} video references`)
    
    return result
  }

  private prepareWritingContext(
    request: InvincibleRequest,
    webResults: WebSearchResult[],
    videos: YouTubeVideoData[],
    analysis: CompetitiveAnalysis
  ): string {
    const topWebSources = webResults.slice(0, 8)
    const topVideos = videos.slice(0, 5)
    
    return `CONTENT CREATION CONTEXT

TOPIC: ${request.topic}
TARGET WORD COUNT: ${request.wordCount || 1500}
TONE: ${request.tone || 'Professional and informative'}
TARGET AUDIENCE: ${request.targetAudience || 'General audience'}
ADDITIONAL INSTRUCTIONS: ${request.additionalInstructions || 'None'}

COMPETITIVE ANALYSIS INSIGHTS:
Content Gaps to Address: ${analysis.gaps.join(', ')}
Opportunities: ${analysis.opportunities.join(', ')}
Best Practices: ${analysis.bestPractices.join(', ')}
Common Structures: ${analysis.commonStructures.join(', ')}
Average Length: ${analysis.averageLength} words

TOP WEB SOURCES:
${topWebSources.map((source, i) => `
${i + 1}. ${source.title}
   URL: ${source.url}
   Key Content: ${source.content.substring(0, 500)}...
`).join('\n')}

YOUTUBE VIDEO INSIGHTS:
${topVideos.map((video, i) => `
${i + 1}. ${video.title} (${video.channel})
   URL: ${video.url}
   Key Points: ${video.captions.slice(0, 10).map(c => c.text).join(' ').substring(0, 300)}...
`).join('\n')}`
  }

  private async generateMainContent(context: string): Promise<string> {
    console.log(`🎯 ${this.agentName}: Generating main content`)
    
    const prompt = `You are an expert content writer creating superior, comprehensive content. Using the provided context, create an exceptional article that surpasses existing content.

${context}

CONTENT REQUIREMENTS:
1. Create content that addresses the identified gaps and opportunities
2. Integrate insights from both web sources and video content
3. Follow the target word count closely
4. Use the specified tone and target audience
5. Include external links naturally within the content (not as citations)
6. Reference 2025 trends and current information
7. Make content data-driven and authoritative
8. Include practical examples and actionable insights
9. Structure content for maximum readability and engagement

CONTENT STRUCTURE:
- Compelling introduction that hooks the reader
- Well-organized main sections with clear headings
- Practical examples and case studies
- Data and statistics where relevant
- Actionable takeaways
- Strong conclusion with key points summary

WRITING STYLE:
- Authoritative yet accessible
- Data-driven with supporting evidence
- Include external links naturally in sentences
- Use current 2025 context and trends
- Avoid generic advice, be specific and actionable
- Write in markdown format with proper headings

Generate the complete article content now:`

    try {
      // Try with Gemini 2.5 Flash Lite first
      const result = await this.gemini.generateContent(prompt)
      console.log(`✅ ${this.agentName}: Content generated successfully with Gemini`)
      return result.response.trim()
    } catch (error) {
      console.warn(`⚠️ Gemini failed, trying with Kimi K2:`, error)
      
      try {
        // Fallback to Kimi K2
        const result = await this.openRouter.generateContent(
          prompt,
          undefined,
          {
            temperature: this.config.temperature,
            maxTokens: this.config.maxTokens
          },
          'Content Generation'
        )
        console.log(`✅ ${this.agentName}: Content generated successfully with Kimi K2`)
        return result.response.trim()
      } catch (fallbackError) {
        console.error(`❌ Both content generation methods failed:`, fallbackError)
        throw new Error('Content generation failed with both AI models')
      }
    }
  }

  private async generateTitle(topic: string, content: string): Promise<string> {
    console.log(`📝 ${this.agentName}: Generating optimized title`)
    
    const prompt = `Based on this topic "${topic}" and the content preview below, generate a compelling, SEO-optimized title that:
- Captures the essence of the content
- Is engaging and click-worthy
- Includes relevant keywords
- Is appropriate for 2025
- Is between 50-60 characters

Content preview: ${content.substring(0, 500)}...

Return ONLY the title, no other text:`

    try {
      const result = await this.openRouter.generateContent(
        prompt,
        undefined,
        {
          temperature: 0.5,
          maxTokens: 100
        },
        'Title Generation'
      )

      return result.response.trim().replace(/^["']|["']$/g, '') // Remove quotes if present
    } catch (error) {
      console.warn(`⚠️ Title generation failed, using fallback`)
      return `The Complete Guide to ${topic} in 2025`
    }
  }

  private generateMetadata(content: string, topic: string): ContentMetadata {
    const words = content.split(/\s+/).filter(word => word.length > 0)
    const wordCount = words.length
    const readingTime = Math.ceil(wordCount / 200) // Average reading speed
    
    // Extract topics (simplified)
    const topics = [topic]
    const contentLower = content.toLowerCase()
    
    // Simple sentiment analysis
    const positiveWords = ['excellent', 'great', 'amazing', 'outstanding', 'effective', 'successful']
    const negativeWords = ['poor', 'bad', 'terrible', 'ineffective', 'failed', 'problematic']
    
    const positiveCount = positiveWords.filter(word => contentLower.includes(word)).length
    const negativeCount = negativeWords.filter(word => contentLower.includes(word)).length
    
    let sentiment = 'neutral'
    if (positiveCount > negativeCount) sentiment = 'positive'
    else if (negativeCount > positiveCount) sentiment = 'negative'
    
    // Simple complexity analysis
    const avgWordsPerSentence = wordCount / (content.split(/[.!?]+/).length || 1)
    const complexity = avgWordsPerSentence > 20 ? 'complex' : avgWordsPerSentence > 15 ? 'moderate' : 'simple'
    
    return {
      wordCount,
      readingTime,
      seoScore: Math.min(95, 70 + (wordCount > 1000 ? 10 : 0) + (topics.length * 5)),
      topics,
      sentiment,
      complexity
    }
  }

  private createSourceReferences(webResults: WebSearchResult[], content: string): SourceReference[] {
    return webResults.slice(0, 10).map(result => ({
      title: result.title,
      url: result.url,
      relevance: result.score,
      usedInSection: this.findUsageSection(result, content)
    }))
  }

  private createYouTubeReferences(videos: YouTubeVideoData[], content: string): YouTubeReference[] {
    return videos.slice(0, 5).map(video => ({
      title: video.title,
      channel: video.channel,
      url: video.url,
      relevance: video.relevanceScore,
      usedInSection: this.findUsageSection(video, content)
    }))
  }

  private findUsageSection(source: any, content: string): string {
    // Simple heuristic to find where source might be referenced
    const contentSections = content.split(/#{1,3}\s+/).filter(section => section.length > 100)
    
    for (let i = 0; i < contentSections.length; i++) {
      const section = contentSections[i].toLowerCase()
      const sourceTitle = source.title.toLowerCase()
      
      // Check if any words from the source title appear in this section
      const titleWords = sourceTitle.split(/\s+/).filter(word => word.length > 3)
      const matchingWords = titleWords.filter(word => section.includes(word))
      
      if (matchingWords.length > 0) {
        // Extract section heading
        const lines = contentSections[i].split('\n')
        return lines[0].substring(0, 50) + '...'
      }
    }
    
    return 'General reference'
  }
}
