import { ResearchAgent } from './ResearchAgent'
import { YouTubeAgent } from './YouTubeAgent'
import { AnalysisAgent } from './AnalysisAgent'
import { WritingAgent } from './WritingAgent'
import { 
  InvincibleRequest, 
  InvincibleResponse, 
  ResearchData, 
  ProgressUpdate,
  AgentConfig 
} from './types'

export class InvincibleSupervisor {
  private researchAgent: ResearchAgent
  private youtubeAgent: YouTubeAgent
  private analysisAgent: AnalysisAgent
  private writingAgent: WritingAgent
  private agentName = 'Invincible .1V Supervisor'
  
  constructor(config?: Partial<AgentConfig>) {
    this.researchAgent = new ResearchAgent(config)
    this.youtubeAgent = new YouTubeAgent(config)
    this.analysisAgent = new AnalysisAgent(config)
    this.writingAgent = new WritingAgent(config)
  }

  async generateContent(
    request: InvincibleRequest,
    onProgress?: (update: ProgressUpdate) => void
  ): Promise<InvincibleResponse> {
    const startTime = Date.now()
    console.log(`🚀 ${this.agentName}: Starting content generation for "${request.topic}"`)
    
    try {
      // Stage 1: Initialization
      this.sendProgress(onProgress, {
        stage: 'initialization',
        progress: 0,
        message: 'Initializing Invincible .1V Super Agent...',
        currentAgent: this.agentName,
        timestamp: new Date().toISOString()
      })

      await this.delay(1000)

      // Stage 2: Research Phase
      this.sendProgress(onProgress, {
        stage: 'research',
        progress: 10,
        message: 'Conducting comprehensive web research...',
        currentAgent: 'Research Agent',
        timestamp: new Date().toISOString()
      })

      const researchResult = await this.researchAgent.performResearch(
        request.topic, 
        request.targetAudience
      )

      this.sendProgress(onProgress, {
        stage: 'research',
        progress: 30,
        message: `Research completed - ${researchResult.results.length} sources found`,
        currentAgent: 'Research Agent',
        data: {
          queries: researchResult.queries,
          webSources: researchResult.results.length,
          webResults: researchResult.results.map(result => ({
            ...result,
            domain: new URL(result.url).hostname,
            favicon: `https://www.google.com/s2/favicons?domain=${new URL(result.url).hostname}&sz=64`
          }))
        },
        timestamp: new Date().toISOString()
      })

      // Stage 3: YouTube Research (with enhanced error handling)
      let youtubeVideos: any[] = []

      if (request.includeYouTube) {
        this.sendProgress(onProgress, {
          stage: 'youtube',
          progress: 40,
          message: 'Searching for relevant YouTube videos...',
          currentAgent: 'YouTube Agent',
          timestamp: new Date().toISOString()
        })

        try {
          youtubeVideos = await this.youtubeAgent.performVideoResearch(request.topic)

          this.sendProgress(onProgress, {
            stage: 'youtube',
            progress: 50,
            message: `YouTube research completed - ${youtubeVideos.length} videos found`,
            currentAgent: 'YouTube Agent',
            data: {
              youtubeVideos: youtubeVideos.length,
              videoResults: youtubeVideos.map(video => ({
                ...video,
                thumbnail: `https://i.ytimg.com/vi/${video.id}/hqdefault.jpg`
              }))
            },
            timestamp: new Date().toISOString()
          })
        } catch (error) {
          console.warn(`⚠️ YouTube research failed, continuing without video content:`, error)
          this.sendProgress(onProgress, {
            stage: 'youtube',
            progress: 50,
            message: 'YouTube research failed - continuing with web content only',
            currentAgent: 'YouTube Agent',
            data: {
              youtubeVideos: 0,
              error: 'YouTube research failed'
            },
            timestamp: new Date().toISOString()
          })
        }
      } else {
        this.sendProgress(onProgress, {
          stage: 'youtube',
          progress: 50,
          message: 'YouTube research skipped - not requested',
          currentAgent: 'YouTube Agent',
          data: {
            youtubeVideos: 0
          },
          timestamp: new Date().toISOString()
        })
      }

      // Stage 4: Competitive Analysis
      this.sendProgress(onProgress, {
        stage: 'analysis',
        progress: 55,
        message: 'Performing competitive analysis...',
        currentAgent: 'Analysis Agent',
        timestamp: new Date().toISOString()
      })

      const analysis = await this.analysisAgent.performCompetitiveAnalysis(
        researchResult.results,
        youtubeVideos,
        request.topic
      )

      this.sendProgress(onProgress, {
        stage: 'analysis',
        progress: 70,
        message: 'Competitive analysis completed',
        currentAgent: 'Analysis Agent',
        data: {
          analysisComplete: true
        },
        timestamp: new Date().toISOString()
      })

      // Stage 5: Content Writing
      this.sendProgress(onProgress, {
        stage: 'writing',
        progress: 75,
        message: 'Generating superior content...',
        currentAgent: 'Writing Agent',
        timestamp: new Date().toISOString()
      })

      const generatedContent = await this.writingAgent.generateContent(
        request,
        researchResult.results,
        youtubeVideos,
        analysis
      )

      this.sendProgress(onProgress, {
        stage: 'writing',
        progress: 95,
        message: 'Content generation completed',
        currentAgent: 'Writing Agent',
        data: {
          wordCount: generatedContent.metadata.wordCount
        },
        timestamp: new Date().toISOString()
      })

      // Stage 6: Completion
      const processingTime = Date.now() - startTime
      const totalSources = researchResult.results.length + youtubeVideos.length

      this.sendProgress(onProgress, {
        stage: 'completed',
        progress: 100,
        message: 'Invincible .1V content generation completed successfully!',
        currentAgent: this.agentName,
        data: {
          wordCount: generatedContent.metadata.wordCount,
          webSources: researchResult.results.length,
          youtubeVideos: youtubeVideos.length
        },
        timestamp: new Date().toISOString()
      })

      // Prepare research data
      const researchData: ResearchData = {
        webResults: researchResult.results,
        youtubeVideos,
        queries: researchResult.queries,
        totalSources
      }

      const response: InvincibleResponse = {
        success: true,
        content: generatedContent,
        research: researchData,
        analysis,
        processingTime,
        totalSources
      }

      console.log(`✅ ${this.agentName}: Content generation completed successfully`)
      console.log(`📊 Final stats:`)
      console.log(`   - Processing time: ${processingTime}ms`)
      console.log(`   - Word count: ${generatedContent.metadata.wordCount}`)
      console.log(`   - Web sources: ${researchResult.results.length}`)
      console.log(`   - YouTube videos: ${youtubeVideos.length}`)
      console.log(`   - Total sources: ${totalSources}`)

      return response

    } catch (error) {
      console.error(`❌ ${this.agentName}: Content generation failed:`, error)
      
      this.sendProgress(onProgress, {
        stage: 'error',
        progress: 0,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        currentAgent: this.agentName,
        timestamp: new Date().toISOString()
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        processingTime: Date.now() - startTime,
        totalSources: 0
      }
    }
  }

  private sendProgress(
    onProgress: ((update: ProgressUpdate) => void) | undefined,
    update: ProgressUpdate
  ): void {
    if (onProgress) {
      onProgress(update)
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // Method to get agent status and capabilities
  getAgentInfo(): {
    name: string
    version: string
    capabilities: string[]
    agents: string[]
  } {
    return {
      name: 'Invincible .1V',
      version: '1.0.0',
      capabilities: [
        'Comprehensive web research using Tavily',
        'YouTube video search and caption extraction',
        'Competitive content analysis',
        'AI-powered content generation',
        'Real-time progress tracking',
        'Multi-source data integration',
        'Autonomous decision making',
        'Superior content creation'
      ],
      agents: [
        'Research Agent (Tavily + Kimi K2)',
        'YouTube Agent (InnerTube + Supadata)',
        'Analysis Agent (Kimi K2 + Gemini)',
        'Writing Agent (Kimi K2 + Gemini 2.5 Flash Lite)',
        'Supervisor Agent (Orchestration)'
      ]
    }
  }
}
